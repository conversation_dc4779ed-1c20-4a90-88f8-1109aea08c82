# coding:utf-8
# test_multi_crawler.py
"""
测试多公众号爬虫功能
"""
import logging
import sys
from automated_crawler import AutomatedCrawler
from excel_auto_crawler import ExcelAutoCrawler

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger()

def test_excel_reading():
    """测试Excel读取功能"""
    print("="*60)
    print("🧪 测试Excel多公众号读取功能")
    print("="*60)
    
    try:
        crawler = ExcelAutoCrawler()
        targets = crawler._get_all_target_urls_from_excel()
        
        print(f"✅ 成功读取到 {len(targets)} 个公众号:")
        for i, target in enumerate(targets, 1):
            print(f"  {i}. {target['name']}")
            print(f"     URL: {target['url'][:80]}...")
            print()
        
        return len(targets) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_automated_crawler():
    """测试自动化爬虫（仅测试初始化，不执行实际爬取）"""
    print("="*60)
    print("🧪 测试自动化爬虫初始化")
    print("="*60)
    
    try:
        logger = setup_logging()
        crawler = AutomatedCrawler()
        
        # 测试读取目标
        targets = crawler._get_all_target_urls_from_excel()
        if targets:
            print(f"✅ 自动化爬虫成功初始化，找到 {len(targets)} 个目标公众号")
            return True
        else:
            print("❌ 未找到任何目标公众号")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 多公众号爬虫功能测试")
    print("="*80)
    
    # 测试1: Excel读取
    test1_result = test_excel_reading()
    
    # 测试2: 自动化爬虫初始化
    test2_result = test_automated_crawler()
    
    # 汇总结果
    print("="*80)
    print("📊 测试结果汇总")
    print("="*80)
    print(f"Excel读取测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"自动化爬虫测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！多公众号功能已就绪。")
        print("\n💡 使用说明:")
        print("1. 确保target_articles.xlsx文件包含多个公众号链接")
        print("2. 运行 python main_enhanced.py 启动自动化爬取")
        print("3. 程序将自动处理所有公众号并生成汇总报告")
        return True
    else:
        print("\n❌ 部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    main()
